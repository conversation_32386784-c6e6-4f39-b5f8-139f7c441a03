import { notFound } from 'next/navigation';
import CTA from '@/components/CTA';
import Image from 'next/image';
import Link from 'next/link';

// Define the comprehensive location data with SEO-optimized content
const locationsData = {
  'toronto': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Toronto, Ontario',
    h1: "Toronto's Trusted Fence Contractor for Quality Fencing",
    h2Main: "Expert Fencing Installation and Repair for Toronto Properties",
    description: "We offer our full range of professional fencing services to homeowners and businesses throughout Toronto. Our local expertise ensures your project complies with all municipal by-laws, from heritage district requirements to pool enclosure regulations, and is built to withstand Southern Ontario's demanding weather.",
    image: '/Images/GTA-Fencing---The-Best-Fence-Contractor-in-Toronto.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Toronto",
        content: "Full coverage from Scarborough to Etobicoke, North York to the Harbourfront → Prompt response times for quotes and emergency repairs."
      },
      {
        title: "Toronto By-Laws & Regulations",
        content: "Deep knowledge of Toronto's specific fence height rules (e.g., front yard vs. backyard) and material restrictions in heritage zones → Guaranteed code-compliant installation for your peace of mind."
      },
      {
        title: "Area-Specific Challenges",
        content: "Solutions for narrow urban lots, shared property lines, and challenging soil conditions → Our local experience provides a tailored, durable fencing solution."
      }
    ],
    
    areasServed: [
      'North York',
      'Scarborough', 
      'Etobicoke',
      'The Beaches',
      'Leaside',
      'Rosedale',
      'Forest Hill',
      'Downtown Toronto'
    ],
    
    faqs: [
      'Do I need a permit to build a fence in Toronto?',
      'What are the rules for a fence bordering a public laneway in Toronto?',
      'Are there specific fence height restrictions for front yards in Toronto?'
    ]
  },
  
  'vaughan': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Vaughan, Ontario',
    h1: "Vaughan's Premier Fence Installation & Repair Services",
    h2Main: "Professional Fence Contractor Services for Vaughan",
    description: "GTA Fencing Company is Vaughan's go-to source for high-quality fencing. From new subdivisions in Kleinburg to established homes in Woodbridge, we provide durable and stylish fencing solutions that enhance property value and meet all of Vaughan's local building codes.",
    image: '/Images/wood-fence-installation-toronto.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Vaughan",
        content: "Complete service across all communities including Woodbridge, Maple, Concord, and Kleinburg → Flexible scheduling to accommodate new home construction timelines."
      },
      {
        title: "Vaughan By-Laws & Regulations",
        content: "Full compliance with City of Vaughan by-laws regarding fence height, materials, and property line setbacks → A smooth, hassle-free installation process."
      },
      {
        title: "Area-Specific Challenges",
        content: "Expertise in fencing for new developments and corner lots with specific visibility requirements → Solutions that meet both aesthetic and safety standards."
      }
    ],
    
    areasServed: [
      'Woodbridge',
      'Maple',
      'Concord',
      'Kleinburg',
      'Thornhill',
      'Sonoma Heights'
    ],
    
    faqs: [
      'What is the maximum fence height allowed in Vaughan\'s residential zones?',
      'Do I need my neighbour\'s permission to build a fence on the property line?',
      'Are there specific rules for fences on corner lots in Vaughan?'
    ]
  },
  
  'richmond-hill': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Richmond Hill, Ontario',
    h1: "Top-Rated Fencing Company in Richmond Hill",
    h2Main: "Quality Fencing Solutions for Richmond Hill Homeowners",
    description: "For beautiful and long-lasting fences in Richmond Hill, look no further than GTA Fencing Company. We respect the unique character of Richmond Hill's communities, from Oak Ridges to Mill Pond, providing installations that are both compliant with local regulations, including those concerning the Oak Ridges Moraine, and aesthetically pleasing.",
    image: '/Images/vinyl-fence-installation.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Richmond Hill",
        content: "Full coverage from Yonge Street to Highway 404, serving all neighborhoods → Meticulous service for established properties and new builds."
      },
      {
        title: "Richmond Hill By-Laws & Regulations",
        content: "Expert knowledge of town by-laws, including special considerations for properties near the Oak Ridges Moraine Conservation Area → Environmentally conscious and fully compliant installations."
      },
      {
        title: "Area-Specific Challenges",
        content: "Solutions that respect mature trees and established landscaping → Careful planning and installation to preserve your property's natural beauty."
      }
    ],
    
    areasServed: [
      'Oak Ridges',
      'Mill Pond',
      'Jefferson',
      'Bayview Hill',
      'Langstaff',
      'Doncrest'
    ],
    
    faqs: [
      'Are there special fencing by-laws for homes on the Oak Ridges Moraine?',
      'What are the Richmond Hill regulations for pool fences?',
      'Who is responsible for the cost of a boundary fence in Richmond Hill?'
    ]
  },
  
  'markham': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Markham, Ontario',
    h1: "Markham's Choice for Professional Fence Installation",
    h2Main: "Expert Fence Building and Repair Services in Markham",
    description: "GTA Fencing Company is proud to serve the diverse communities of Markham. Whether you're in historic Unionville or a modern Cornell development, we deliver superior fencing solutions that align with community standards and adhere strictly to the City of Markham's property by-laws.",
    image: '/Images/chain-link-fence-installation-toronto.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Markham",
        content: "Prompt and professional service in Unionville, Thornhill, Cornell, and all other Markham neighborhoods → We cover all areas of this thriving city."
      },
      {
        title: "Markham By-Laws & Regulations",
        content: "In-depth understanding of Markham's Fence By-law, including height, material, and location specifics → Ensuring your project is approved without delay."
      },
      {
        title: "Area-Specific Challenges",
        content: "Designs that complement both historic and modern architectural styles → A fence that enhances your home and fits the neighborhood character."
      }
    ],
    
    areasServed: [
      'Unionville',
      'Thornhill',
      'Cornell',
      'Angus Glen',
      'Berczy Village',
      'Milliken Mills'
    ],
    
    faqs: [
      'Does the City of Markham have rules about which side of the fence should face the neighbour?',
      'Can I install a fence in my front yard in Markham?',
      'What is the process for resolving a fence dispute with a neighbour in Markham?'
    ]
  },
  
  'mississauga': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Mississauga, Ontario',
    h1: "Mississauga's Leading Fence Contractor",
    h2Main: "Complete Fencing Services for Mississauga Residents & Businesses",
    description: "From the waterfront homes of Port Credit to the family-friendly streets of Streetsville, GTA Fencing Company is Mississauga's most trusted fence contractor. We navigate the specific by-laws of Canada's seventh-largest city to deliver high-quality, durable fencing solutions for any residential or commercial property.",
    image: '/Images/Fence-installation-contractor-toronto.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Mississauga",
        content: "City-wide coverage including Port Credit, Streetsville, Clarkson, Erin Mills, and Malton → Fast service for repairs and new installations."
      },
      {
        title: "Mississauga By-Laws & Regulations",
        content: "Comprehensive knowledge of the Mississauga Fence By-law for residential, commercial, and industrial zones → We handle the details so your project is stress-free."
      },
      {
        title: "Area-Specific Challenges",
        content: "Fencing solutions for diverse lot sizes, from large suburban yards to compact townhomes → Maximizing privacy and security for any property type."
      }
    ],
    
    areasServed: [
      'Port Credit',
      'Streetsville',
      'Clarkson',
      'Erin Mills',
      'Lorne Park',
      'Cooksville',
      'Meadowvale'
    ],
    
    faqs: [
      'What are the height restrictions for fences in Mississauga?',
      'Do I need a permit for a fence around my swimming pool in Mississauga?',
      'Are there rules about using barbed wire on fences in Mississauga?'
    ]
  },
  
  'brampton': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Brampton, Ontario',
    h1: "Brampton's Expert Fence Installation and Repair Company",
    h2Main: "Your Trusted Local Fence Contractor in Brampton",
    description: "GTA Fencing Company provides the residents and businesses of Brampton with top-tier fencing services. We understand the needs of this dynamic city, offering durable, attractive, and affordable fencing options that comply with all of Brampton's municipal standards, from Bramalea to Mount Pleasant.",
    image: '/Images/garden-fencing-installation.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Brampton",
        content: "Serving all of Brampton's neighborhoods, including Bramalea, Heart Lake, and Mount Pleasant → Responsive service for a fast-growing community."
      },
      {
        title: "Brampton By-Laws & Regulations",
        content: "Expert guidance on Brampton's specific fence by-laws, including corner lot visibility triangles and pool enclosure rules → A fully compliant and safe installation is our priority."
      },
      {
        title: "Area-Specific Challenges",
        content: "Cost-effective and durable solutions suitable for Brampton's diverse housing styles and commercial properties → The right fence for your budget and needs."
      }
    ],
    
    areasServed: [
      'Bramalea',
      'Heart Lake',
      'Mount Pleasant',
      'Springdale',
      'Downtown Brampton',
      'Fletcher&apos;s Meadow'
    ],
    
    faqs: [
      'What is the maximum fence height allowed on residential property in Brampton?',
      'Are there special fencing requirements for a corner lot in Brampton?',
      'How do I find my property line before installing a fence?'
    ]
  },
  
  'oakville': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Oakville, Ontario',
    h1: "Premium Fencing Solutions for Discerning Homes in Oakville",
    h2Main: "Custom Fence Design and Installation in Oakville",
    description: "GTA Fencing Company offers premium fence installation and repair services tailored to the beautiful homes of Oakville. From elegant wrought iron in Morrison to durable vinyl in Glen Abbey, we provide high-end solutions that enhance property aesthetics and comply with all Town of Oakville regulations, especially for lakeside properties.",
    image: '/Images/Wood-fence-in-the-garden.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Oakville",
        content: "Serving all of Oakville's prestigious neighborhoods, including Bronte, Glen Abbey, and Joshua Creek → White-glove service from consultation to cleanup."
      },
      {
        title: "Oakville By-Laws & Regulations",
        content: "Meticulous adherence to Oakville's stringent by-laws and aesthetic standards → A fence that complements your home and neighborhood."
      },
      {
        title: "Area-Specific Challenges",
        content: "Material recommendations to withstand the moisture and wind from Lake Ontario → Enhanced durability and longevity for your investment."
      }
    ],
    
    areasServed: [
      'Bronte',
      'Glen Abbey',
      'Joshua Creek',
      'Kerr Village',
      'Morrison',
      'West Oak Trails'
    ],
    
    faqs: [
      'What types of fences are best suited for properties near Lake Ontario?',
      'Does the Town of Oakville have specific by-laws for heritage properties?',
      'Are there height restrictions for fences in Oakville\'s front yards?'
    ]
  },
  
  'burlington': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Burlington, Ontario',
    h1: "Burlington's Go-To Company for Quality Fencing",
    h2Main: "Professional Fence Installation & Repair Across Burlington",
    description: "Serving the vibrant city of Burlington, GTA Fencing Company is your expert partner for all fencing needs. We provide residents from Aldershot to Millcroft with durable, code-compliant fences that respect Burlington's natural beauty, including properties near the Niagara Escarpment.",
    image: '/Images/Removable-Mesh-Pool-Fence-Toronto.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Burlington",
        content: "Full service coverage from the lakefront to the escarpment, including Aldershot, Roseland, and Millcroft → Prompt and reliable local service."
      },
      {
        title: "Burlington By-Laws & Regulations",
        content: "Deep understanding of Burlington's fence by-laws and special considerations for properties within the Niagara Escarpment planning area → A responsible and compliant approach."
      },
      {
        title: "Area-Specific Challenges",
        content: "Solutions for properties with sloped terrain and unique lot shapes → Expert installation on even the most challenging landscapes."
      }
    ],
    
    areasServed: [
      'Aldershot',
      'Roseland',
      'Millcroft',
      'Alton Village',
      'The Orchard',
      'Downtown Burlington'
    ],
    
    faqs: [
      'Do I need a permit for a standard backyard fence in Burlington?',
      'Are there any special fence regulations for homes near the Niagara Escarpment?',
      'What are the rules for swimming pool enclosures in Burlington?'
    ]
  },
  
  'whitby': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Whitby, Ontario',
    h1: "Professional Fence Contractor Serving Whitby",
    h2Main: "Complete Fencing Solutions for Whitby Homes and Businesses",
    description: "GTA Fencing Company is proud to serve the growing community of Whitby. From the family-friendly neighborhoods of Brooklin to the established areas downtown, we install high-quality, long-lasting fences that provide security, privacy, and curb appeal, all while meeting the Town of Whitby's standards.",
    image: '/Images/chain-link-fence-contractor-toronto.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Whitby",
        content: "Full service in all Whitby communities, including Brooklin, Williamsburg, and the Port Whitby area → We are your local fence experts."
      },
      {
        title: "Whitby By-Laws & Regulations",
        content: "Thorough knowledge of the Town of Whitby's fence by-law to ensure your project is completed correctly → A hassle-free process from start to finish."
      },
      {
        title: "Area-Specific Challenges",
        content: "Durable fence solutions perfect for new suburban developments and older established lots → The right fence to match your home's style and needs."
      }
    ],
    
    areasServed: [
      'Brooklin',
      'Williamsburg',
      'Pringle Creek',
      'Port Whitby',
      'Taunton North',
      'Downtown Whitby'
    ],
    
    faqs: [
      'What is the maximum allowed height for a backyard fence in Whitby?',
      'Are there any restrictions on fence materials in Whitby?',
      'Do I need a permit for fence repairs in Whitby?'
    ]
  },
  
  'oshawa': {
    seoTitle: 'GTA Fencing Company - Fence Contractor - Oshawa, Ontario',
    h1: "Oshawa's Top-Rated Fence Installation & Repair Service",
    h2Main: "Your Expert Fence Contractor in Oshawa",
    description: "GTA Fencing Company delivers reliable and affordable fencing services throughout Oshawa. Whether you're in a historic central neighborhood or a new subdivision in the north end, we provide expert installation and quality materials to ensure your new fence is a lasting investment for your property.",
    image: '/Images/GTA-Fencing---The-Best-Fence-Contractor-in-Toronto-2.jpg',
    
    residentialServices: [
      { name: 'Pool Fencing', slug: 'pool-fencing' },
      { name: 'Wood Fencing', slug: 'wood-fencing' },
      { name: 'Vinyl Fencing', slug: 'vinyl-fencing' },
      { name: 'Chain Link Fencing', slug: 'chain-link-fencing' },
      { name: 'Wrought Iron Fencing', slug: 'wrought-iron-fencing' }
    ],
    
    commercialServices: [
      { name: 'Perimeter Fencing', slug: 'perimeter-fencing' },
      { name: 'Security Fencing', slug: 'security-fencing' },
      { name: 'Industrial Chain Link Fencing', slug: 'industrial-chain-link-fencing' }
    ],
    
    maintenanceServices: [
      { name: 'Fence Maintenance', slug: 'fence-maintenance' },
      { name: 'Fence Repair Services', slug: 'fence-repair' }
    ],
    
    localTaskNodes: [
      {
        title: "Service Availability in Oshawa",
        content: "City-wide service from the lakefront to the northern communities like Windfields → Responsive and dependable service across Oshawa."
      },
      {
        title: "Oshawa By-Laws & Regulations",
        content: "Full compliance with the City of Oshawa's fence by-law, including division fence regulations → We build fences the right way."
      },
      {
        title: "Area-Specific Challenges",
        content: "Fencing solutions that cater to the diverse mix of housing, from century homes to brand new builds → The perfect fence to complement any property."
      }
    ],
    
    areasServed: [
      'Northwood',
      'Samac',
      'Lakeview',
      'Windfields',
      'Kedron',
      'Downtown Oshawa'
    ],
    
    faqs: [
      'Who is responsible for paying for a fence between two properties in Oshawa?',
      'What are the pool fence requirements in the City of Oshawa?',
      'How close to the sidewalk can I build a fence in my front yard?'
    ]
  }
};

type LocationPageProps = {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export default async function LocationPage({ params }: LocationPageProps) {
  const { slug } = await params;
  const location = locationsData[slug as keyof typeof locationsData];

  if (!location) {
    notFound();
  }

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-deep-navy text-white py-16">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{location.h1}</h1>
              <p className="text-lg md:text-xl mb-8 leading-relaxed">{location.description}</p>
              <CTA text="Get Your Free Quote" link="/contact" />
            </div>
            <div className="relative h-64 md:h-96">
              <Image
                src={location.image}
                alt={location.h1}
                fill
                className="object-cover rounded-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-warm-off-white">
        <div className="container mx-auto">
          <h2 className="text-2xl md:text-3xl font-semibold mb-8 text-warm-gold text-center">{location.h2Main}</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {/* Residential Fencing */}
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h4 className="text-2xl font-bold text-deep-navy mb-6">Residential Fencing</h4>
              <ul className="space-y-3">
                {location.residentialServices.map((service, index) => (
                  <li key={index}>
                    <Link 
                      href={`/services/${service.slug}`}
                      className="text-rich-charcoal hover:text-warm-gold transition-colors duration-200 flex items-center"
                    >
                      <span className="mr-2">•</span>
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Commercial Fencing */}
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h4 className="text-2xl font-bold text-deep-navy mb-6">Commercial Fencing</h4>
              <ul className="space-y-3">
                {location.commercialServices.map((service, index) => (
                  <li key={index}>
                    <Link
                      href={`/services/${service.slug}`}
                      className="text-rich-charcoal hover:text-warm-gold transition-colors duration-200 flex items-center"
                    >
                      <span className="mr-2">•</span>
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Fence Maintenance and Repair */}
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h4 className="text-2xl font-bold text-deep-navy mb-6">Fence Maintenance and Repair</h4>
              <ul className="space-y-3">
                {location.maintenanceServices.map((service, index) => (
                  <li key={index}>
                    <Link
                      href={`/services/${service.slug}`}
                      className="text-rich-charcoal hover:text-warm-gold transition-colors duration-200 flex items-center"
                    >
                      <span className="mr-2">•</span>
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Local Task Node Bullets Section */}
      <section className="py-16">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-3 gap-8">
            {location.localTaskNodes.map((node, index) => (
              <div key={index} className="bg-soft-beige p-8 rounded-lg">
                <h3 className="text-xl font-bold text-deep-navy mb-4">{node.title}</h3>
                <p className="text-rich-charcoal leading-relaxed">{node.content}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Local Areas We Serve Section */}
      <section className="py-16 bg-warm-off-white">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-deep-navy text-center mb-12">
            Local Areas We Serve Near {slug.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {location.areasServed.map((area, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
                <h3 className="font-semibold text-rich-charcoal">{area}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold text-deep-navy text-center mb-12">
            Frequently Asked Questions about Fencing in {slug.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
          </h2>
          <div className="space-y-6">
            {location.faqs.map((faq, index) => (
              <div key={index} className="bg-soft-beige p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-deep-navy mb-2">{faq}</h3>
                <p className="text-rich-charcoal">
                  Contact our local experts for detailed information about {faq.toLowerCase()}.
                  We&apos;re here to help you navigate all local regulations and requirements.
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Conversion CTA Section */}
      <section className="py-20 bg-deep-navy text-white text-center">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started with Your Fencing Project?</h2>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            Get your free, no-obligation quote today. Our local experts are ready to help you with
            all your fencing needs in {slug.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <CTA text="Get Free Quote" link="/contact" />
            <Link
              href="/services"
              className="bg-transparent border-2 border-warm-gold text-warm-gold px-8 py-3 rounded-lg font-semibold hover:bg-warm-gold hover:text-deep-navy transition-colors duration-200"
            >
              View All Services
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}

// Generate static params for all locations
export async function generateStaticParams() {
  return Object.keys(locationsData).map((slug) => ({
    slug,
  }));
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const location = locationsData[slug as keyof typeof locationsData];

  if (!location) {
    return {
      title: 'Location Not Found',
    };
  }

  return {
    title: location.seoTitle,
    description: location.description,
  };
}
